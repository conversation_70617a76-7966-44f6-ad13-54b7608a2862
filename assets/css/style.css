/* CSS Custom Properties for consistent colors and values */
:root {
  --primary-red: #dc2626;
  --primary-red-hover: #b91c1c;
  --primary-red-light: #ef4444;
  --background-gray: #f3f4f6;
  --text-dark: #222;
  --text-gray: #666;
  --text-light-gray: #9ca3af;
  --white: #fff;
  --dark-bg: #111827;
  --border-gray: #1f2937;
  --red-light-bg: #fee2e2;
  --transition-fast: 0.2s;
  --border-radius-full: 9999px;
  --border-radius-lg: 16px;
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
}

body {
  font-family: sans-serif;
  background: var(--background-gray);
  margin: 0;
  padding: 0;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.transition {
  transition: color var(--transition-fast), background var(--transition-fast);
}

.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-full);
  font-weight: bold;
  text-decoration: none;
  text-align: center;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary-red);
  color: var(--white);
  transition: background var(--transition-fast);
}

.btn-primary:hover {
  background: var(--primary-red-hover);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--white);
  color: var(--white);
  transition: background var(--transition-fast), color var(--transition-fast);
}

.btn-outline:hover {
  background: var(--white);
  color: var(--text-dark);
}

.btn-white {
  background: var(--white);
  color: var(--primary-red);
  transition: background var(--transition-fast);
}

.btn-white:hover {
  background: var(--background-gray);
}

/* Base title styles */
.title {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
}

.title-section {
  margin-bottom: var(--spacing-2xl);
}

.title-courses {
  margin-bottom: var(--spacing-md);
}

header {
  background: var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: fixed;
  width: 100%;
  z-index: 50;
}

.header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
}

.logo {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-red);
  text-decoration: none;
}

.logo span {
  color: var(--text-dark);
}
nav {
  display: none;
}

.nav-links {
  display: flex;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--text-dark);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.nav-link:hover {
  color: var(--primary-red);
}

.hamburger {
  display: block;
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-dark);
  cursor: pointer;
}

.mobile-menu {
  display: none;
  position: fixed;
  inset: 0;
  background: var(--white);
  z-index: 100;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.mobile-menu.active {
  display: flex;
}

.mobile-menu a {
  font-size: 2rem;
  color: var(--text-dark);
  text-decoration: none;
  margin: var(--spacing-md) 0;
  transition: color var(--transition-fast);
}

.mobile-menu a:hover {
  color: var(--primary-red);
}

.close-menu {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 2rem;
  color: var(--text-dark);
  cursor: pointer;
}

.hero-image {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: var(--spacing-3xl);
  background: var(--text-dark)
    url("../img/starter.jpg")
    center/cover no-repeat;
}

.hero-content {
  color: var(--white);
  max-width: 700px;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: var(--spacing-lg);
}

.hero-desc {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xl);
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 400px;
}
@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
  }
}
section {
  padding: var(--spacing-3xl) 0;
}

.why-choose {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
}

.why-card {
  background: var(--background-gray);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
}

.why-icon {
  background: var(--red-light-bg);
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-md);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.why-icon i {
  color: var(--primary-red);
  font-size: 2rem;
}

.why-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: var(--spacing-sm);
}

.why-desc {
  color: var(--text-gray);
}
@media (min-width: 768px) {
  .why-choose {
    grid-template-columns: repeat(3, 1fr);
  }
}
.courses-desc {
  text-align: center;
  color: var(--text-gray);
  max-width: 600px;
  margin: 0 auto var(--spacing-2xl);
}

.courses-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
}
@media (min-width: 768px) {
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .courses-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
.class-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.class-card img {
  width: 100%;
  height: 192px;
  object-fit: cover;
}

.class-card-content {
  padding: var(--spacing-lg);
}

.class-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.class-tag {
  background: var(--red-light-bg);
  color: var(--primary-red);
  padding: 4px var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: 0.9rem;
  font-weight: bold;
}

.class-time {
  color: var(--text-gray);
  font-size: 0.9rem;
}

.class-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
}

.class-desc {
  color: var(--text-gray);
  margin-bottom: var(--spacing-md);
}

.class-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.class-price {
  font-weight: bold;
  color: var(--primary-red);
}

.class-link {
  color: var(--primary-red);
  font-weight: bold;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.class-link:hover {
  color: var(--primary-red-hover);
}
.courses-btn {
  margin-top: var(--spacing-2xl);
}

.cta-section {
  background: var(--primary-red);
  color: var(--white);
  text-align: center;
  padding: var(--spacing-3xl) 0;
}

.cta-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: var(--spacing-lg);
}

.cta-desc {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
footer {
  background: var(--dark-bg);
  color: var(--white);
  padding: var(--spacing-2xl) 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
}

.footer-logo {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-red);
  margin-bottom: var(--spacing-md);
}

.footer-logo span {
  color: var(--white);
}

.footer-social {
  display: flex;
  gap: var(--spacing-md);
}

.footer-social a {
  color: var(--white);
  text-decoration: none;
  font-size: 1.2rem;
  transition: color var(--transition-fast);
}

.footer-social a:hover {
  color: var(--primary-red-light);
}

.footer-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: var(--spacing-md);
}

.footer-links,
.footer-hours,
.footer-contact {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li,
.footer-hours li,
.footer-contact li {
  margin-bottom: var(--spacing-xs);
}

.footer-links a {
  color: var(--white);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-red-light);
}

.footer-bottom {
  border-top: 1px solid var(--border-gray);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  text-align: center;
  color: var(--text-light-gray);
  font-size: 0.95rem;
}

/* Consolidated Media Queries */
@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
  }
}

@media (min-width: 768px) {
  nav {
    display: block;
  }

  .hamburger {
    display: none;
  }

  .mobile-menu {
    display: none !important;
  }

  .why-choose {
    grid-template-columns: repeat(3, 1fr);
  }

  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .courses-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
